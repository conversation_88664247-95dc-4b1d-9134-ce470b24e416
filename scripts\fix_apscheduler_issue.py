#!/usr/bin/env python3
"""
سكريبت لإصلاح مشكلة APScheduler المكتشفة في السجلات
"""

import subprocess
import sys
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_apscheduler():
    """فحص حالة مكتبة APScheduler"""
    try:
        import apscheduler
        logger.info(f"✅ APScheduler متاحة - الإصدار: {apscheduler.__version__}")
        return True
    except ImportError:
        logger.error("❌ APScheduler غير متاحة")
        return False

def install_apscheduler():
    """تثبيت أو إعادة تثبيت APScheduler"""
    try:
        logger.info("🔄 جاري تثبيت APScheduler...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "APScheduler==3.10.4"])
        logger.info("✅ تم تثبيت APScheduler بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ فشل في تثبيت APScheduler: {e}")
        return False

def verify_installation():
    """التحقق من نجاح التثبيت"""
    try:
        from apscheduler.schedulers.asyncio import AsyncIOScheduler
        from apscheduler.schedulers.background import BackgroundScheduler
        logger.info("✅ تم التحقق من APScheduler - جميع الوحدات متاحة")
        return True
    except ImportError as e:
        logger.error(f"❌ فشل في التحقق من APScheduler: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    logger.info("🔧 بدء إصلاح مشكلة APScheduler...")
    
    # فحص الحالة الحالية
    if check_apscheduler():
        logger.info("ℹ️ APScheduler متاحة بالفعل، سيتم التحقق من الوحدات...")
        if verify_installation():
            logger.info("✅ APScheduler تعمل بشكل صحيح")
            return
    
    # إعادة التثبيت
    if install_apscheduler():
        if verify_installation():
            logger.info("🎉 تم إصلاح مشكلة APScheduler بنجاح!")
        else:
            logger.error("❌ فشل في التحقق بعد التثبيت")
    else:
        logger.error("❌ فشل في إصلاح المشكلة")

if __name__ == "__main__":
    main()
